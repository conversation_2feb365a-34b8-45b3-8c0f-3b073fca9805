import { 
  sanitize, 
  validate, 
  validateFields, 
  errorMessages 
} from '@/lib/validation/schemas'

describe('Input Validation and Sanitization', () => {
  describe('sanitize functions', () => {
    describe('sanitize.html', () => {
      it('should remove HTML tags', () => {
        expect(sanitize.html('<script>alert("xss")</script>Hello')).toBe('alert(xss)Hello')
        expect(sanitize.html('<div>Content</div>')).toBe('Content')
        expect(sanitize.html('<p>Paragraph <strong>bold</strong></p>')).toBe('Paragraph bold')
      })

      it('should remove dangerous characters', () => {
        expect(sanitize.html('Hello<>&"\'')).toBe('Hello')
        expect(sanitize.html('Safe content')).toBe('Safe content')
      })

      it('should trim whitespace', () => {
        expect(sanitize.html('  Hello World  ')).toBe('Hello World')
      })
    })

    describe('sanitize.email', () => {
      it('should convert to lowercase and trim', () => {
        expect(sanitize.email('  <EMAIL>  ')).toBe('<EMAIL>')
        expect(sanitize.email('<EMAIL>')).toBe('<EMAIL>')
      })
    })

    describe('sanitize.phone', () => {
      it('should keep only valid phone characters', () => {
        expect(sanitize.phone('+****************')).toBe('+****************')
        expect(sanitize.phone('555.123.4567abc')).toBe('5551234567')
        expect(sanitize.phone('invalid phone!')).toBe('')
      })
    })

    describe('sanitize.number', () => {
      it('should convert strings to numbers', () => {
        expect(sanitize.number('123.45')).toBe(123.45)
        expect(sanitize.number('invalid')).toBe(0)
        expect(sanitize.number(42)).toBe(42)
      })
    })

    describe('sanitize.price', () => {
      it('should ensure positive numbers with max 2 decimal places', () => {
        expect(sanitize.price('123.456')).toBe(123.46)
        expect(sanitize.price('-50')).toBe(0)
        expect(sanitize.price('99.9')).toBe(99.9)
      })
    })

    describe('sanitize.url', () => {
      it('should validate and return proper URLs', () => {
        expect(sanitize.url('https://example.com')).toBe('https://example.com/')
        expect(sanitize.url('invalid-url')).toBe('')
        expect(sanitize.url('http://test.com/path?query=1')).toBe('http://test.com/path?query=1')
      })
    })
  })

  describe('validate functions', () => {
    describe('validate.email', () => {
      it('should validate correct email formats', () => {
        expect(validate.email('<EMAIL>')).toBe(true)
        expect(validate.email('<EMAIL>')).toBe(true)
      })

      it('should reject invalid email formats', () => {
        expect(validate.email('invalid-email')).toBe(false)
        expect(validate.email('@domain.com')).toBe(false)
        expect(validate.email('user@')).toBe(false)
        expect(validate.email('')).toBe(false)
      })
    })

    describe('validate.phone', () => {
      it('should validate correct phone formats', () => {
        expect(validate.phone('+1234567890')).toBe(true)
        expect(validate.phone('1234567890')).toBe(true)
        expect(validate.phone('+****************')).toBe(true)
      })

      it('should reject invalid phone formats', () => {
        expect(validate.phone('123')).toBe(false)
        expect(validate.phone('abc123')).toBe(false)
        expect(validate.phone('')).toBe(false)
      })
    })

    describe('validate.url', () => {
      it('should validate correct URLs', () => {
        expect(validate.url('https://example.com')).toBe(true)
        expect(validate.url('http://test.org/path')).toBe(true)
        expect(validate.url('ftp://files.example.com')).toBe(true)
      })

      it('should reject invalid URLs', () => {
        expect(validate.url('not-a-url')).toBe(false)
        expect(validate.url('http://')).toBe(false)
        expect(validate.url('')).toBe(false)
      })
    })

    describe('validate.price', () => {
      it('should validate positive numbers with max 2 decimal places', () => {
        expect(validate.price(99.99)).toBe(true)
        expect(validate.price(0)).toBe(true)
        expect(validate.price(100)).toBe(true)
      })

      it('should reject invalid prices', () => {
        expect(validate.price(-1)).toBe(false)
        expect(validate.price(99.999)).toBe(false)
        expect(validate.price(Infinity)).toBe(false)
        expect(validate.price(NaN)).toBe(false)
      })
    })

    describe('validate.stockCount', () => {
      it('should validate non-negative integers', () => {
        expect(validate.stockCount(0)).toBe(true)
        expect(validate.stockCount(100)).toBe(true)
        expect(validate.stockCount(1)).toBe(true)
      })

      it('should reject invalid stock counts', () => {
        expect(validate.stockCount(-1)).toBe(false)
        expect(validate.stockCount(1.5)).toBe(false)
        expect(validate.stockCount(NaN)).toBe(false)
      })
    })

    describe('validate.requiredString', () => {
      it('should validate non-empty strings', () => {
        expect(validate.requiredString('Hello')).toBe(true)
        expect(validate.requiredString('A', 1)).toBe(true)
        expect(validate.requiredString('Hello World', 5)).toBe(true)
      })

      it('should reject empty or short strings', () => {
        expect(validate.requiredString('')).toBe(false)
        expect(validate.requiredString('   ')).toBe(false)
        expect(validate.requiredString('Hi', 5)).toBe(false)
      })
    })

    describe('validate.nonEmptyArray', () => {
      it('should validate non-empty arrays', () => {
        expect(validate.nonEmptyArray([1, 2, 3])).toBe(true)
        expect(validate.nonEmptyArray(['item'])).toBe(true)
      })

      it('should reject empty arrays or non-arrays', () => {
        expect(validate.nonEmptyArray([])).toBe(false)
        expect(validate.nonEmptyArray('not-array' as any)).toBe(false)
        expect(validate.nonEmptyArray(null as any)).toBe(false)
      })
    })
  })

  describe('validateFields', () => {
    it('should validate multiple fields and return results', () => {
      const data = {
        email: '<EMAIL>',
        name: 'John Doe',
        age: 25
      }

      const rules = {
        email: (value: string) => validate.email(value),
        name: (value: string) => validate.requiredString(value, 2),
        age: (value: number) => value >= 18
      }

      const result = validateFields(data, rules)
      expect(result.isValid).toBe(true)
      expect(result.errors).toHaveLength(0)
    })

    it('should collect validation errors', () => {
      const data = {
        email: 'invalid-email',
        name: '',
        age: 15
      }

      const rules = {
        email: (value: string) => validate.email(value),
        name: (value: string) => validate.requiredString(value, 2),
        age: (value: number) => value >= 18 || 'Must be at least 18 years old'
      }

      const result = validateFields(data, rules)
      expect(result.isValid).toBe(false)
      expect(result.errors).toHaveLength(3)
      expect(result.errors).toContain('Must be at least 18 years old')
    })
  })

  describe('errorMessages', () => {
    it('should provide consistent error message functions', () => {
      expect(errorMessages.required('Name')).toBe('Name is required')
      expect(errorMessages.invalid('Email')).toBe('Email is invalid')
      expect(errorMessages.tooShort('Password', 8)).toBe('Password must be at least 8 characters')
      expect(errorMessages.tooLong('Title', 100)).toBe('Title must be no more than 100 characters')
      expect(errorMessages.notFound('User')).toBe('User not found')
      expect(errorMessages.alreadyExists('Product')).toBe('Product already exists')
    })

    it('should provide specific validation messages', () => {
      expect(errorMessages.invalidEmail).toBe('Please enter a valid email address')
      expect(errorMessages.invalidPhone).toBe('Please enter a valid phone number')
      expect(errorMessages.invalidUrl).toBe('Please enter a valid URL')
      expect(errorMessages.invalidPrice).toBe('Price must be a positive number with at most 2 decimal places')
      expect(errorMessages.invalidStock).toBe('Stock count must be a non-negative integer')
      expect(errorMessages.unauthorized).toBe('You are not authorized to perform this action')
    })
  })

  describe('XSS Prevention', () => {
    it('should prevent script injection in HTML sanitization', () => {
      const maliciousInputs = [
        '<script>alert("xss")</script>',
        '<img src="x" onerror="alert(1)">',
        '<div onclick="alert(1)">Click me</div>',
        'javascript:alert(1)',
        '<iframe src="javascript:alert(1)"></iframe>'
      ]

      maliciousInputs.forEach(input => {
        const sanitized = sanitize.html(input)
        expect(sanitized).not.toContain('<script')
        expect(sanitized).not.toContain('javascript:')
        expect(sanitized).not.toContain('onclick')
        expect(sanitized).not.toContain('onerror')
      })
    })
  })

  describe('SQL Injection Prevention', () => {
    it('should handle potentially dangerous characters in strings', () => {
      const dangerousInputs = [
        "'; DROP TABLE users; --",
        "' OR '1'='1",
        "admin'--",
        "' UNION SELECT * FROM passwords --"
      ]

      dangerousInputs.forEach(input => {
        const sanitized = sanitize.html(input)
        // Should remove dangerous characters
        expect(sanitized).not.toContain("'")
        expect(sanitized).not.toContain('<')
        expect(sanitized).not.toContain('>')
      })
    })
  })
})
