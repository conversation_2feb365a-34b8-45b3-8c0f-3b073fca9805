import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { auth } from "./auth";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";

// Query to get all suppliers
export const getSuppliers = query({
  args: {
    activeOnly: v.optional(v.boolean()),
  },
  handler: async (ctx, { activeOnly = false }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_VIEW);

    let query = ctx.db.query("suppliers");

    if (activeOnly) {
      query = query.withIndex("by_active", (q) => q.eq("isActive", true));
    }

    const suppliers = await query.collect();

    // Get product count for each supplier
    const suppliersWithStats = await Promise.all(
      suppliers.map(async (supplier) => {
        const products = await ctx.db
          .query("products")
          .withIndex("by_supplier", (q) => q.eq("supplierId", supplier._id))
          .collect();

        const activeProducts = products.filter(p => p.status === "active").length;
        const totalProducts = products.length;

        return {
          ...supplier,
          productCount: {
            active: activeProducts,
            total: totalProducts,
          },
        };
      })
    );

    return suppliersWithStats;
  },
});

// Query to get a single supplier by ID
export const getSupplier = query({
  args: { id: v.id("suppliers") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_VIEW);

    const supplier = await ctx.db.get(id);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Get products for this supplier
    const products = await ctx.db
      .query("products")
      .withIndex("by_supplier", (q) => q.eq("supplierId", id))
      .collect();

    const createdBy = await ctx.db.get(supplier.createdBy);

    return {
      ...supplier,
      products: products.map(p => ({
        id: p._id,
        title: p.title,
        status: p.status,
        stockCount: p.stockCount,
        finalPrice: p.finalPrice,
      })),
      createdByUser: createdBy ? { name: createdBy.name, email: createdBy.email } : null,
    };
  },
});

// Mutation to create a new supplier
export const createSupplier = mutation({
  args: {
    name: v.string(),
    contactInfo: v.object({
      email: v.optional(v.string()),
      phone: v.optional(v.string()),
      address: v.optional(v.string()),
    }),
    platformUrl: v.optional(v.string()),
    rating: v.optional(v.number()),
    notes: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_CREATE);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate input
    if (!args.name.trim()) {
      throw new Error("Supplier name is required");
    }

    // Check if supplier with same name already exists
    const existingSupplier = await ctx.db
      .query("suppliers")
      .filter((q) => q.eq(q.field("name"), args.name))
      .first();

    if (existingSupplier) {
      throw new Error("Supplier with this name already exists");
    }

    // Validate email if provided
    if (args.contactInfo.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(args.contactInfo.email)) {
        throw new Error("Invalid email format");
      }
    }

    // Validate rating if provided
    if (args.rating !== undefined && (args.rating < 0 || args.rating > 5)) {
      throw new Error("Rating must be between 0 and 5");
    }

    const supplierId = await ctx.db.insert("suppliers", {
      ...args,
      isActive: args.isActive ?? true,
      createdBy: userId,
    });

    return supplierId;
  },
});

// Mutation to update a supplier
export const updateSupplier = mutation({
  args: {
    id: v.id("suppliers"),
    name: v.optional(v.string()),
    contactInfo: v.optional(v.object({
      email: v.optional(v.string()),
      phone: v.optional(v.string()),
      address: v.optional(v.string()),
    })),
    platformUrl: v.optional(v.string()),
    rating: v.optional(v.number()),
    notes: v.optional(v.string()),
    isActive: v.optional(v.boolean()),
  },
  handler: async (ctx, { id, ...updates }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_EDIT);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const supplier = await ctx.db.get(id);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Validate name if being updated
    if (updates.name) {
      if (!updates.name.trim()) {
        throw new Error("Supplier name is required");
      }

      // Check if another supplier with same name exists
      const existingSupplier = await ctx.db
        .query("suppliers")
        .filter((q) => 
          q.and(
            q.eq(q.field("name"), updates.name),
            q.neq(q.field("_id"), id)
          )
        )
        .first();

      if (existingSupplier) {
        throw new Error("Supplier with this name already exists");
      }
    }

    // Validate email if being updated
    if (updates.contactInfo?.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(updates.contactInfo.email)) {
        throw new Error("Invalid email format");
      }
    }

    // Validate rating if being updated
    if (updates.rating !== undefined && (updates.rating < 0 || updates.rating > 5)) {
      throw new Error("Rating must be between 0 and 5");
    }

    await ctx.db.patch(id, updates);

    return { success: true };
  },
});

// Mutation to delete a supplier (soft delete by setting isActive to false)
export const deleteSupplier = mutation({
  args: { id: v.id("suppliers") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_DELETE);

    const supplier = await ctx.db.get(id);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Check if supplier has active products
    const activeProducts = await ctx.db
      .query("products")
      .withIndex("by_supplier", (q) => q.eq("supplierId", id))
      .filter((q) => q.eq(q.field("status"), "active"))
      .collect();

    if (activeProducts.length > 0) {
      throw new Error("Cannot delete supplier with active products. Please archive the products first.");
    }

    await ctx.db.patch(id, {
      isActive: false,
    });

    return { success: true };
  },
});

// Query to search suppliers by name
export const searchSuppliers = query({
  args: {
    searchTerm: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { searchTerm, limit = 20 }) => {
    await requirePermission(ctx, PERMISSIONS.SUPPLIERS_VIEW);

    const suppliers = await ctx.db.query("suppliers").collect();

    // Simple text search
    const searchResults = suppliers
      .filter(supplier => 
        supplier.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        (supplier.notes && supplier.notes.toLowerCase().includes(searchTerm.toLowerCase()))
      )
      .slice(0, limit);

    // Get product count for each result
    const resultsWithStats = await Promise.all(
      searchResults.map(async (supplier) => {
        const products = await ctx.db
          .query("products")
          .withIndex("by_supplier", (q) => q.eq("supplierId", supplier._id))
          .collect();

        const activeProducts = products.filter(p => p.status === "active").length;

        return {
          ...supplier,
          productCount: {
            active: activeProducts,
            total: products.length,
          },
        };
      })
    );

    return resultsWithStats;
  },
});

// Query to get supplier statistics
export const getSupplierStats = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    const suppliers = await ctx.db.query("suppliers").collect();

    const stats = {
      total: suppliers.length,
      active: suppliers.filter(s => s.isActive).length,
      inactive: suppliers.filter(s => !s.isActive).length,
      withProducts: 0,
      withoutProducts: 0,
    };

    // Count suppliers with/without products
    for (const supplier of suppliers) {
      const products = await ctx.db
        .query("products")
        .withIndex("by_supplier", (q) => q.eq("supplierId", supplier._id))
        .collect();

      if (products.length > 0) {
        stats.withProducts++;
      } else {
        stats.withoutProducts++;
      }
    }

    return stats;
  },
});
