// convex/auth/rbac.ts

import { v } from "convex/values";
import { mutation, query, type QueryCtx, type MutationCtx } from "../_generated/server";
import { getPermissionsForRole, type Role, type Permission } from "./permissions";
import { getAuthUserId } from "@convex-dev/auth/server";
import { Id } from "../_generated/dataModel";

export type AdminUserWithPermissions = {
  _id: Id<"adminUsers">;
  userId: Id<"users">;
  role: Role;
  permissions: readonly Permission[]; // Match the readonly return type
  createdBy: Id<"users">;
  isActive: boolean;
  lastLoginAt?: number;
};

// Query to get current user's admin profile
export const getCurrentAdminUser = query({
  args: {},
  handler: async (ctx): Promise<AdminUserWithPermissions | null> => {
    const userId = await getAuthUserId(ctx);
    if (!userId) {
      return null;
    }

    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (!adminUser || !adminUser.isActive) {
      return null;
    }

    return {
      _id: adminUser._id,
      userId: adminUser.userId,
      role: adminUser.role as Role,
      // This is a computed property for the client, so returning the readonly array is fine.
      permissions: getPermissionsForRole(adminUser.role as Role),
      createdBy: adminUser.createdBy,
      isActive: adminUser.isActive,
      lastLoginAt: adminUser.lastLoginAt,
    };
  },
});

// Query to check if current user has a specific permission
export const hasPermission = query({
  args: { permission: v.string() },
  handler: async (ctx, { permission }) => {
    try {
      // We can reuse our helper. It will throw on failure, which we catch.
      await requirePermission(ctx, permission as Permission);
      return true;
    } catch {
      return false;
    }
  },
});

// Query to get all admin users
export const getAdminUsers = query({
  args: {},
  handler: async (ctx) => {
    // REFACTOR 1: Use the helper to simplify permission checking.
    await requirePermission(ctx, "admin_users:view");

    const adminUsers = await ctx.db.query("adminUsers").collect();
    
    const adminUsersWithDetails = await Promise.all(
      adminUsers.map(async (admin) => {
        const user = await ctx.db.get(admin.userId);
        return {
          ...admin,
          role: admin.role as Role,
          permissions: getPermissionsForRole(admin.role as Role),
          // FIX 3: Safely handle potentially missing user details.
          user: user ? { 
            name: user.name ?? "Unknown Name",
            email: user.email ?? "No Email",
            image: user.image ?? undefined
          } : null,
        };
      })
    );

    return adminUsersWithDetails;
  },
});

// Mutation to create a new admin user
export const createAdminUser = mutation({
  args: {
    userId: v.id("users"),
    role: v.union(v.literal("super_admin"), v.literal("admin"), v.literal("moderator")),
  },
  handler: async (ctx, { userId, role }) => {
    // REFACTOR 1 (Again): Simplify permission checking.
    const currentUserId = await requirePermissionAndGetUserId(ctx, "admin_users:create");

    const existingAdmin = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (existingAdmin) {
      throw new Error("User is already an admin");
    }

    const user = await ctx.db.get(userId);
    if (!user) {
      throw new Error("User not found");
    }

    const adminUserId = await ctx.db.insert("adminUsers", {
      userId,
      role,
      // FIX 1: Convert readonly array to a mutable one.
      permissions: [...getPermissionsForRole(role as Role)],
      createdBy: currentUserId,
      isActive: true,
    });

    return adminUserId;
  },
});

// Mutation to update admin user role
export const updateAdminUserRole = mutation({
  args: {
    adminUserId: v.id("adminUsers"),
    role: v.union(v.literal("super_admin"), v.literal("admin"), v.literal("moderator")),
  },
  handler: async (ctx, { adminUserId, role }) => {
    const { currentAdmin } = await requirePermissionAndGetAdmin(ctx, "admin_users:edit");

    const adminUserToUpdate = await ctx.db.get(adminUserId);
    if (!adminUserToUpdate) {
      throw new Error("Admin user not found");
    }

    if (adminUserToUpdate.role === "super_admin" && currentAdmin.role !== "super_admin") {
      throw new Error("Only super admins can modify super admin accounts");
    }

    await ctx.db.patch(adminUserId, {
      role,
      // FIX 1 (Again): Convert readonly array to a mutable one.
      permissions: [...getPermissionsForRole(role as Role)],
    });

    return { success: true };
  },
});

// Mutation to deactivate an admin user
export const deactivateAdminUser = mutation({
  args: {
    adminUserId: v.id("adminUsers"),
  },
  handler: async (ctx, { adminUserId }) => {
    const { currentUserId, currentAdmin } = await requirePermissionAndGetAdmin(ctx, "admin_users:delete");

    const adminUserToDeactivate = await ctx.db.get(adminUserId);
    if (!adminUserToDeactivate) {
      throw new Error("Admin user not found");
    }

    if (adminUserToDeactivate.userId === currentUserId) {
      throw new Error("Cannot deactivate your own account");
    }

    if (adminUserToDeactivate.role === "super_admin" && currentAdmin.role !== "super_admin") {
      throw new Error("Only super admins can deactivate super admin accounts");
    }

    await ctx.db.patch(adminUserId, {
      isActive: false,
    });

    return { success: true };
  },
});

// Mutation to update last login time for the current admin user
export const updateLastLogin = mutation({
  args: {},
  handler: async (ctx) => {
    const userId = await getAuthUserId(ctx);
    if (!userId) { return; } // No error needed, just exit if not logged in

    const adminUser = await ctx.db
      .query("adminUsers")
      .withIndex("by_user", (q) => q.eq("userId", userId))
      .first();

    if (adminUser) {
      await ctx.db.patch(adminUser._id, {
        lastLoginAt: Date.now(),
      });
    }
  },
});

// Helper function to check permission and throw an error if not authorized
async function requirePermission(
  ctx: QueryCtx | MutationCtx,
  permission: Permission
): Promise<void> {
  await requirePermissionAndGetAdmin(ctx, permission);
}

// Helper function that both checks permission and returns the current admin's user ID
async function requirePermissionAndGetUserId(
  ctx: QueryCtx | MutationCtx,
  permission: Permission
) {
  const { currentUserId } = await requirePermissionAndGetAdmin(ctx, permission);
  return currentUserId;
}

// Centralized helper to get the current admin, check permissions, and throw if invalid
async function requirePermissionAndGetAdmin(
  ctx: QueryCtx | MutationCtx,
  permission: Permission
) {
  const currentUserId = await getAuthUserId(ctx);
  if (!currentUserId) {
    throw new Error("Not authenticated");
  }

  const currentAdmin = await ctx.db
    .query("adminUsers")
    .withIndex("by_user", (q) => q.eq("userId", currentUserId))
    .first();

  if (!currentAdmin || !currentAdmin.isActive) {
    throw new Error("Access denied: Not an active admin user.");
  }

  const permissions = getPermissionsForRole(currentAdmin.role as Role);
  if (!permissions.includes(permission)) {
    throw new Error(`Insufficient permissions: '${permission}' required.`);
  }

  return { currentAdmin, currentUserId };
}