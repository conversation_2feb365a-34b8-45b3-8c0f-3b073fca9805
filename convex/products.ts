import { v } from "convex/values";
import { mutation, query } from "./_generated/server";
import { auth } from "./auth";
import { requirePermission } from "./auth/rbac";
import { PERMISSIONS } from "./auth/permissions";

// Query to get all products with pagination
export const getProducts = query({
  args: {
    paginationOpts: v.optional(v.object({
      numItems: v.number(),
      cursor: v.optional(v.string()),
    })),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
    supplierId: v.optional(v.id("suppliers")),
  },
  handler: async (ctx, { paginationOpts, status, supplierId }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    let query = ctx.db.query("products");

    // Apply filters
    if (status) {
      query = query.withIndex("by_status", (q) => q.eq("status", status));
    } else if (supplierId) {
      query = query.withIndex("by_supplier", (q) => q.eq("supplierId", supplierId));
    }

    // Apply pagination
    if (paginationOpts) {
      const results = await query.paginate(paginationOpts);
      
      // Get supplier info for each product
      const productsWithSuppliers = await Promise.all(
        results.page.map(async (product) => {
          const supplier = await ctx.db.get(product.supplierId);
          return {
            ...product,
            supplier: supplier ? { name: supplier.name, id: supplier._id } : null,
          };
        })
      );

      return {
        ...results,
        page: productsWithSuppliers,
      };
    }

    // Get all products without pagination
    const products = await query.collect();
    const productsWithSuppliers = await Promise.all(
      products.map(async (product) => {
        const supplier = await ctx.db.get(product.supplierId);
        return {
          ...product,
          supplier: supplier ? { name: supplier.name, id: supplier._id } : null,
        };
      })
    );

    return productsWithSuppliers;
  },
});

// Query to get a single product by ID
export const getProduct = query({
  args: { id: v.id("products") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    const product = await ctx.db.get(id);
    if (!product) {
      throw new Error("Product not found");
    }

    const supplier = await ctx.db.get(product.supplierId);
    const createdBy = await ctx.db.get(product.createdBy);
    const updatedBy = await ctx.db.get(product.updatedBy);

    return {
      ...product,
      supplier: supplier ? { name: supplier.name, id: supplier._id } : null,
      createdByUser: createdBy ? { name: createdBy.name, email: createdBy.email } : null,
      updatedByUser: updatedBy ? { name: updatedBy.name, email: updatedBy.email } : null,
    };
  },
});

// Mutation to create a new product
export const createProduct = mutation({
  args: {
    title: v.string(),
    description: v.string(),
    curationNotes: v.string(),
    supplierId: v.id("suppliers"),
    priceInYuan: v.number(),
    serviceFee: v.number(),
    finalPrice: v.number(),
    tags: v.array(v.string()),
    images: v.array(v.string()),
    stockCount: v.number(),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
  },
  handler: async (ctx, args) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_CREATE);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    // Validate supplier exists
    const supplier = await ctx.db.get(args.supplierId);
    if (!supplier) {
      throw new Error("Supplier not found");
    }

    // Validate input
    if (args.priceInYuan <= 0 || args.finalPrice <= 0) {
      throw new Error("Prices must be positive");
    }

    if (args.stockCount < 0) {
      throw new Error("Stock count cannot be negative");
    }

    if (!args.title.trim() || !args.description.trim()) {
      throw new Error("Title and description are required");
    }

    const productId = await ctx.db.insert("products", {
      ...args,
      status: args.status || "active",
      createdBy: userId,
      updatedBy: userId,
    });

    return productId;
  },
});

// Mutation to update a product
export const updateProduct = mutation({
  args: {
    id: v.id("products"),
    title: v.optional(v.string()),
    description: v.optional(v.string()),
    curationNotes: v.optional(v.string()),
    supplierId: v.optional(v.id("suppliers")),
    priceInYuan: v.optional(v.number()),
    serviceFee: v.optional(v.number()),
    finalPrice: v.optional(v.number()),
    tags: v.optional(v.array(v.string())),
    images: v.optional(v.array(v.string())),
    stockCount: v.optional(v.number()),
    status: v.optional(v.union(v.literal("active"), v.literal("inactive"), v.literal("archived"))),
  },
  handler: async (ctx, { id, ...updates }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_EDIT);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const product = await ctx.db.get(id);
    if (!product) {
      throw new Error("Product not found");
    }

    // Validate supplier if being updated
    if (updates.supplierId) {
      const supplier = await ctx.db.get(updates.supplierId);
      if (!supplier) {
        throw new Error("Supplier not found");
      }
    }

    // Validate prices if being updated
    if (updates.priceInYuan !== undefined && updates.priceInYuan <= 0) {
      throw new Error("Price in Yuan must be positive");
    }

    if (updates.finalPrice !== undefined && updates.finalPrice <= 0) {
      throw new Error("Final price must be positive");
    }

    if (updates.stockCount !== undefined && updates.stockCount < 0) {
      throw new Error("Stock count cannot be negative");
    }

    await ctx.db.patch(id, {
      ...updates,
      updatedBy: userId,
    });

    return { success: true };
  },
});

// Mutation to delete a product (soft delete by setting status to archived)
export const deleteProduct = mutation({
  args: { id: v.id("products") },
  handler: async (ctx, { id }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_DELETE);

    const userId = await auth.getUserId(ctx);
    if (!userId) {
      throw new Error("Not authenticated");
    }

    const product = await ctx.db.get(id);
    if (!product) {
      throw new Error("Product not found");
    }

    await ctx.db.patch(id, {
      status: "archived",
      updatedBy: userId,
    });

    return { success: true };
  },
});

// Query to search products by title or tags
export const searchProducts = query({
  args: {
    searchTerm: v.string(),
    limit: v.optional(v.number()),
  },
  handler: async (ctx, { searchTerm, limit = 20 }) => {
    await requirePermission(ctx, PERMISSIONS.PRODUCTS_VIEW);

    const products = await ctx.db.query("products")
      .filter((q) => 
        q.or(
          q.eq(q.field("status"), "active"),
          q.eq(q.field("status"), "inactive")
        )
      )
      .collect();

    // Simple text search (in production, you might want to use a proper search service)
    const searchResults = products
      .filter(product => 
        product.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
        product.tags.some(tag => tag.toLowerCase().includes(searchTerm.toLowerCase()))
      )
      .slice(0, limit);

    // Get supplier info for each result
    const resultsWithSuppliers = await Promise.all(
      searchResults.map(async (product) => {
        const supplier = await ctx.db.get(product.supplierId);
        return {
          ...product,
          supplier: supplier ? { name: supplier.name, id: supplier._id } : null,
        };
      })
    );

    return resultsWithSuppliers;
  },
});

// Query to get product statistics
export const getProductStats = query({
  args: {},
  handler: async (ctx) => {
    await requirePermission(ctx, PERMISSIONS.ANALYTICS_VIEW);

    const products = await ctx.db.query("products").collect();

    const stats = {
      total: products.length,
      active: products.filter(p => p.status === "active").length,
      inactive: products.filter(p => p.status === "inactive").length,
      archived: products.filter(p => p.status === "archived").length,
      lowStock: products.filter(p => p.stockCount < 10 && p.status === "active").length,
      outOfStock: products.filter(p => p.stockCount === 0 && p.status === "active").length,
    };

    return stats;
  },
});
