{"name": "ma<PERSON>o", "version": "0.1.0", "private": true, "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "next dev", "dev:backend": "convex dev", "predev": "convex dev --until-success && convex dev --once --run-sh \"node setup.mjs --once\" && convex dashboard", "build": "next build", "start": "next start", "lint": "next lint", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:security": "jest __tests__/security"}, "dependencies": {"@convex-dev/auth": "^0.0.81", "@upstash/ratelimit": "^2.0.6", "@upstash/redis": "^1.35.3", "convex": "^1.26.1", "next": "15.2.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@testing-library/jest-dom": "^6.8.0", "@testing-library/react": "^16.3.0", "@testing-library/user-event": "^14.6.1", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.7", "eslint": "^9", "eslint-config-next": "15.2.3", "jest": "^30.0.5", "jest-environment-jsdom": "^30.0.5", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}