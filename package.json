{"name": "ma<PERSON>o", "version": "0.1.0", "private": true, "scripts": {"dev": "npm-run-all --parallel dev:frontend dev:backend", "dev:frontend": "next dev", "dev:backend": "convex dev", "predev": "convex dev --until-success && convex dev --once --run-sh \"node setup.mjs --once\" && convex dashboard", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@convex-dev/auth": "^0.0.81", "convex": "^1.26.0", "next": "15.2.3", "react": "^19.0.0", "react-dom": "^19.0.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "dotenv": "^16.4.7", "eslint": "^9", "eslint-config-next": "15.2.3", "npm-run-all": "^4.1.5", "prettier": "^3.5.3", "tailwindcss": "^4", "typescript": "^5"}}